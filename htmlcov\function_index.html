<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">30%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-07-30 10:49 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1___init___py.html">llm_proxy_server\__init__.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t16">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t16"><data value='init__'>AuthManager.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t21">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t21"><data value='load_users'>AuthManager._load_users</data></a></td>
                <td>23</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="16 23">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t59">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t59"><data value='authenticate'>AuthManager.authenticate</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t90">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t90"><data value='reload_users'>AuthManager.reload_users</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t95">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t95"><data value='get_user_count'>AuthManager.get_user_count</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t99">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t99"><data value='is_user_admin'>AuthManager.is_user_admin</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t111">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t111"><data value='get_current_admin_user'>get_current_admin_user</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html#t41">llm_proxy_server\config.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html#t41"><data value='init__'>Settings.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html#t66">llm_proxy_server\config.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html#t66"><data value='get_settings'>get_settings</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html">llm_proxy_server\config.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t75">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t75"><data value='set_model_registry_refresh_callback'>set_model_registry_refresh_callback</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t80">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t80"><data value='get_config_manager'>get_config_manager</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t97">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t97"><data value='get_hosts'>get_hosts</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t111">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t111"><data value='add_host'>add_host</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t120">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t120"><data value='update_host'>update_host</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t130">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t130"><data value='delete_host'>delete_host</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t139">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t139"><data value='test_host_connection'>test_host_connection</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t148">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t148"><data value='generate_on_host'>generate_on_host</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t165">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t165"><data value='get_host_models'>get_host_models</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t174">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t174"><data value='install_model_on_host'>install_model_on_host</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t189">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t189"><data value='remove_model_from_host'>remove_model_from_host</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t205">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t205"><data value='get_users'>get_users</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t214">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t214"><data value='add_user'>add_user</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t223">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t223"><data value='update_user'>update_user</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t233">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t233"><data value='delete_user'>delete_user</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t243">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t243"><data value='list_backups'>list_backups</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t252">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t252"><data value='restore_backup'>restore_backup</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t262">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t262"><data value='get_system_settings'>get_system_settings</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t287">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t287"><data value='validate_hosts_config'>validate_hosts_config</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t325">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t325"><data value='export_configuration'>export_configuration</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t347">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html#t347"><data value='config_health_check'>config_health_check</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html">llm_proxy_server\config_api.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_api_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>96</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="96 96">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t20">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t20"><data value='init__'>ConfigManager.__init__</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t36">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t36"><data value='set_model_registry_refresh_callback'>ConfigManager.set_model_registry_refresh_callback</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t40">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t40"><data value='trigger_model_registry_refresh'>ConfigManager._trigger_model_registry_refresh</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t49">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t49"><data value='create_backup'>ConfigManager._create_backup</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t63">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t63"><data value='get_hosts'>ConfigManager.get_hosts</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t83">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t83"><data value='add_host'>ConfigManager.add_host</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t125">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t125"><data value='update_host'>ConfigManager.update_host</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t163">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t163"><data value='delete_host'>ConfigManager.delete_host</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t195">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t195"><data value='test_host_connection'>ConfigManager.test_host_connection</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t221">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t221"><data value='get_host_models'>ConfigManager.get_host_models</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t263">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t263"><data value='install_model_on_host'>ConfigManager.install_model_on_host</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t293">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t293"><data value='remove_model_from_host'>ConfigManager.remove_model_from_host</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t330">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t330"><data value='generate_on_host'>ConfigManager.generate_on_host</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t368">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t368"><data value='get_users'>ConfigManager.get_users</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t395">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t395"><data value='add_user'>ConfigManager.add_user</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t424">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t424"><data value='update_user'>ConfigManager.update_user</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t474">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t474"><data value='delete_user'>ConfigManager.delete_user</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t519">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t519"><data value='list_backups'>ConfigManager.list_backups</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t543">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html#t543"><data value='restore_backup'>ConfigManager.restore_backup</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html">llm_proxy_server\config_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t16">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t16"><data value='init__'>LoadBalancer.__init__</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t30">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t30"><data value='select_host'>LoadBalancer.select_host</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t61">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t61"><data value='round_robin_select'>LoadBalancer._round_robin_select</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t70">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t70"><data value='least_connections_select'>LoadBalancer._least_connections_select</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t84">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t84"><data value='weighted_random_select'>LoadBalancer._weighted_random_select</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t110">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t110"><data value='random_select'>LoadBalancer._random_select</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t116">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t116"><data value='fastest_select'>LoadBalancer._fastest_select</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t144">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t144"><data value='adaptive_select'>LoadBalancer._adaptive_select</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t182">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t182"><data value='increment_connections'>LoadBalancer.increment_connections</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t187">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t187"><data value='decrement_connections'>LoadBalancer.decrement_connections</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t193">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t193"><data value='set_host_weight'>LoadBalancer.set_host_weight</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t198">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t198"><data value='set_host_health'>LoadBalancer.set_host_health</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t206">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t206"><data value='record_response_time'>LoadBalancer.record_response_time</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t223">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t223"><data value='get_host_response_stats'>LoadBalancer.get_host_response_stats</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t245">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t245"><data value='get_host_stats'>LoadBalancer.get_host_stats</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t265">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t265"><data value='reset_stats'>LoadBalancer.reset_stats</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t66">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t66"><data value='get_current_user'>get_current_user</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t95">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t95"><data value='lifespan'>lifespan</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t114">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t114"><data value='health_check_loop'>health_check_loop</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t168">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t168"><data value='login_page'>login_page</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t179">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t179"><data value='admin_interface'>admin_interface</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t190">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t190"><data value='config_interface'>config_interface</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t201">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t201"><data value='logging_middleware'>logging_middleware</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t223">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t223"><data value='chat'>chat</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t233">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t233"><data value='stream_chat'>chat.stream_chat</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t253">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t253"><data value='generate'>generate</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t265">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t265"><data value='stream_generate'>generate.stream_generate</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t285">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t285"><data value='embed'>embed</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t299">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t299"><data value='show'>show</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t318">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t318"><data value='list_models'>list_models</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t340">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t340"><data value='version'>version</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t355">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t355"><data value='pull_model'>pull_model</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t373">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t373"><data value='delete_model'>delete_model</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t391">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t391"><data value='get_model_status'>get_model_status</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t411">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t411"><data value='get_status'>get_status</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t429">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t429"><data value='get_metrics'>get_metrics</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t459">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t459"><data value='reset_metrics'>reset_metrics</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t480">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t480"><data value='debug_hosts'>debug_hosts</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t504">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t504"><data value='cleanup_orphaned_hosts'>cleanup_orphaned_hosts</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t537">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t537"><data value='save_metrics'>save_metrics</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t559">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t559"><data value='health_check'>health_check</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t581">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t581"><data value='debug_generate_request'>debug_generate_request</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t593">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t593"><data value='root'>root</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>87</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="87 87">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>125</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="125 125">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t32">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t32"><data value='init__'>MetricsManager.__init__</data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t89">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t89"><data value='record_request'>MetricsManager.record_request</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t165">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t165"><data value='record_token_stats'>MetricsManager._record_token_stats</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t198">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t198"><data value='record_health_check'>MetricsManager.record_health_check</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t212">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t212"><data value='increment_active_connections'>MetricsManager.increment_active_connections</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t216">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t216"><data value='decrement_active_connections'>MetricsManager.decrement_active_connections</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t221">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t221"><data value='get_request_stats'>MetricsManager.get_request_stats</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t242">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t242"><data value='get_host_stats'>MetricsManager.get_host_stats</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t316">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t316"><data value='get_model_stats'>MetricsManager.get_model_stats</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t363">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t363"><data value='get_token_stats'>MetricsManager.get_token_stats</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t386">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t386"><data value='get_health_status'>MetricsManager.get_health_status</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t410">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t410"><data value='get_recent_activity'>MetricsManager.get_recent_activity</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t414">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t414"><data value='generate_metrics'>MetricsManager.generate_metrics</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t433">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t433"><data value='reset_metrics'>MetricsManager.reset_metrics</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t465">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t465"><data value='cleanup_orphaned_hosts'>MetricsManager.cleanup_orphaned_hosts</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t508">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t508"><data value='load_metrics'>MetricsManager._load_metrics</data></a></td>
                <td>9</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="6 9">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t527">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t527"><data value='save_metrics'>MetricsManager._save_metrics</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t550">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t550"><data value='to_dict'>MetricsManager._to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t580">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t580"><data value='restore_from_dict'>MetricsManager._restore_from_dict</data></a></td>
                <td>50</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="50 50">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t655">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t655"><data value='save_metrics_now'>MetricsManager.save_metrics_now</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t36">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t36"><data value='init__'>ProxyManager.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t45">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t45"><data value='initialize'>ProxyManager.initialize</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t56">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t56"><data value='shutdown'>ProxyManager.shutdown</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t62">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t62"><data value='load_host_configs'>ProxyManager._load_host_configs</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t79">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t79"><data value='initialize_clients'>ProxyManager._initialize_clients</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t91">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t91"><data value='discover_models'>ProxyManager._discover_models</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t116">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t116"><data value='refresh_model_registry'>ProxyManager.refresh_model_registry</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t121">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t121"><data value='handle_chat_stream'>ProxyManager.handle_chat_stream</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t208">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t208"><data value='handle_chat'>ProxyManager.handle_chat</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t324">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t324"><data value='handle_generate_stream'>ProxyManager.handle_generate_stream</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t420">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t420"><data value='handle_generate'>ProxyManager.handle_generate</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t543">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t543"><data value='handle_embed'>ProxyManager.handle_embed</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t580">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t580"><data value='handle_show'>ProxyManager.handle_show</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t600">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t600"><data value='get_client_for_model'>ProxyManager._get_client_for_model</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t636">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t636"><data value='get_host_for_client'>ProxyManager._get_host_for_client</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t643">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t643"><data value='list_models'>ProxyManager.list_models</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t667">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t667"><data value='get_status'>ProxyManager.get_status</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t692">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t692"><data value='pull_model'>ProxyManager.pull_model</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t764">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t764"><data value='delete_model'>ProxyManager.delete_model</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t839">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t839"><data value='get_model_status'>ProxyManager.get_model_status</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t857">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t857"><data value='perform_health_checks'>ProxyManager.perform_health_checks</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1942</td>
                <td>1353</td>
                <td>3</td>
                <td class="right" data-ratio="589 1942">30%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-07-30 10:49 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
